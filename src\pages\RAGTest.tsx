import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ragLLMService } from '@/services/ragLLMService';
import { Language } from '@/types/common';
import { AVAILABLE_MODELS, LLMModel } from '@/contexts/ModelSettingsContext';
import ModelSelection from '@/components/Game/ModelSelection';

const RAGTest: React.FC = () => {
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [language, setLanguage] = useState<Language>('EN');
  const [selectedModel, setSelectedModel] = useState<LLMModel>('meta-llama/llama-3.3-8b-instruct:free');
  const [allModelResults, setAllModelResults] = useState<any>({});
  const [testingAllModels, setTestingAllModels] = useState(false);

  // Expose debug functions to window for console testing
  React.useEffect(() => {
    (window as any).testOpenRouter = async (model?: LLMModel) => {
      console.log('Testing OpenRouter from console...');
      const result = await ragLLMService.testOpenRouterAPI(model);
      console.log('Console test result:', result);
      return result;
    };

    (window as any).testAllModels = async () => {
      console.log('Testing all models from console...');
      const results: any = {};
      for (const model of AVAILABLE_MODELS) {
        console.log(`Testing model: ${model.id}`);
        const result = await ragLLMService.testOpenRouterAPI(model.id);
        results[model.id] = result;
        console.log(`Result for ${model.id}:`, result);
      }
      return results;
    };

    // Auto-run environment check on page load
    showEnvironmentInfo();

    // Auto-run API key test after a short delay
    setTimeout(() => {
      testAPIKeyValidity();
    }, 1000);
  }, []);

  const testRAGService = async () => {
    setIsLoading(true);
    setTestResult(null);
    
    try {
      // Test with a common item name
      const result = await ragLLMService.generateFeedback('paper', 'newspaper', language, selectedModel);
      setTestResult(result);
    } catch (error) {
      setTestResult({ error: error.toString() });
    } finally {
      setIsLoading(false);
    }
  };

  const testWithDifferentItems = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      const testItems = [
        { binType: 'plastic', itemName: 'plastic bottle' },
        { binType: 'glass', itemName: 'wine bottle' },
        { binType: 'bio', itemName: 'apple core' }
      ];

      const results = [];
      for (const item of testItems) {
        const result = await ragLLMService.generateFeedback(item.binType, item.itemName, language, selectedModel);
        results.push({ item: item.itemName, binType: item.binType, response: result });
      }

      setTestResult({ multipleTests: results });
    } catch (error) {
      setTestResult({ error: error.toString() });
    } finally {
      setIsLoading(false);
    }
  };

  const testOpenRouterAPI = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      const result = await ragLLMService.testOpenRouterAPI(selectedModel);
      setTestResult({ apiTest: result });
    } catch (error) {
      setTestResult({ error: error.toString() });
    } finally {
      setIsLoading(false);
    }
  };

  const testAllModels = async () => {
    setTestingAllModels(true);
    setAllModelResults({});

    const results: any = {};

    for (const model of AVAILABLE_MODELS) {
      console.log(`Testing model: ${model.id}`);
      try {
        const result = await ragLLMService.testOpenRouterAPI(model.id);
        results[model.id] = {
          success: !result.error,
          data: result,
          modelName: model.name
        };
      } catch (error) {
        results[model.id] = {
          success: false,
          error: error.toString(),
          modelName: model.name
        };
      }
    }

    setAllModelResults(results);
    setTestingAllModels(false);
  };

  const showEnvironmentInfo = () => {
    const envInfo = {
      VITE_OPENROUTER_API_KEY: import.meta.env.VITE_OPENROUTER_API_KEY ?
        `${import.meta.env.VITE_OPENROUTER_API_KEY.substring(0, 10)}...` : 'NOT SET',
      VITE_SITE_URL: import.meta.env.VITE_SITE_URL || 'NOT SET',
      VITE_SITE_NAME: import.meta.env.VITE_SITE_NAME || 'NOT SET',
      availableModels: AVAILABLE_MODELS.map(m => m.id),
      selectedModel: selectedModel
    };
    setTestResult({ envInfo });
  };

  const quickTestCurrentModel = async () => {
    setIsLoading(true);
    try {
      console.log('Quick testing current model:', selectedModel);
      const result = await ragLLMService.testOpenRouterAPI(selectedModel);
      setTestResult({
        quickTest: {
          model: selectedModel,
          result: result
        }
      });
    } catch (error) {
      setTestResult({ error: error.toString() });
    } finally {
      setIsLoading(false);
    }
  };

  const testAPIKeyValidity = async () => {
    setIsLoading(true);
    try {
      console.log('Testing API key validity...');
      const result = await ragLLMService.testAPIKeyValidity();
      setTestResult({
        apiKeyTest: result
      });
    } catch (error) {
      setTestResult({ error: error.toString() });
    } finally {
      setIsLoading(false);
    }
  };

  const testBasicModel = async () => {
    setIsLoading(true);
    try {
      console.log('Testing with a basic model...');
      // Try with a simple model that should definitely work
      const result = await ragLLMService.testOpenRouterAPI('openai/gpt-3.5-turbo');
      setTestResult({
        basicModelTest: {
          model: 'openai/gpt-3.5-turbo',
          result: result
        }
      });
    } catch (error) {
      setTestResult({ error: error.toString() });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-gray-900 dark:text-white">
          RAG LLM Service Test
        </h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 mb-6 shadow-lg">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            Test Configuration
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Language:
              </label>
              <select
                value={language}
                onChange={(e) => setLanguage(e.target.value as Language)}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="EN">English</option>
                <option value="DE">German</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Model:
              </label>
              <select
                value={selectedModel}
                onChange={(e) => setSelectedModel(e.target.value as LLMModel)}
                className="w-full border border-gray-300 dark:border-gray-600 rounded px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                {AVAILABLE_MODELS.map(model => (
                  <option key={model.id} value={model.id}>
                    {model.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="space-x-4 mb-4">
            <Button
              onClick={testRAGService}
              disabled={isLoading || testingAllModels}
              className="mr-4"
            >
              {isLoading ? 'Testing...' : 'Test Single Item (Newspaper)'}
            </Button>
            <Button
              onClick={testWithDifferentItems}
              disabled={isLoading || testingAllModels}
              variant="outline"
            >
              {isLoading ? 'Testing...' : 'Test Multiple Items'}
            </Button>
            <Button
              onClick={testOpenRouterAPI}
              disabled={isLoading || testingAllModels}
              variant="secondary"
            >
              {isLoading ? 'Testing...' : 'Test OpenRouter API'}
            </Button>
          </div>

          <div className="border-t pt-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Model Diagnostics
            </h3>
            <div className="space-y-2">
              <Button
                onClick={testAPIKeyValidity}
                disabled={isLoading || testingAllModels}
                variant="secondary"
                className="w-full"
              >
                {isLoading ? 'Testing...' : 'Test API Key & Available Models'}
              </Button>
              <Button
                onClick={testBasicModel}
                disabled={isLoading || testingAllModels}
                variant="default"
                className="w-full"
              >
                {isLoading ? 'Testing...' : 'Test Basic Model (GPT-3.5)'}
              </Button>
              <Button
                onClick={quickTestCurrentModel}
                disabled={isLoading || testingAllModels}
                variant="default"
                className="w-full"
              >
                {isLoading ? 'Testing...' : `Quick Test: ${selectedModel.split('/').pop()}`}
              </Button>
              <Button
                onClick={testAllModels}
                disabled={isLoading || testingAllModels}
                variant="destructive"
                className="w-full"
              >
                {testingAllModels ? 'Testing All Models...' : 'Test All 3 Models (Diagnostic)'}
              </Button>
              <Button
                onClick={showEnvironmentInfo}
                disabled={isLoading || testingAllModels}
                variant="outline"
                className="w-full"
              >
                Show Environment Info
              </Button>
            </div>
          </div>
        </div>

        {Object.keys(allModelResults).length > 0 && (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg mb-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              All Models Test Results
            </h2>
            <div className="space-y-4">
              {Object.entries(allModelResults).map(([modelId, result]: [string, any]) => (
                <div key={modelId} className="border border-gray-200 dark:border-gray-600 rounded p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {result.modelName}
                    </h3>
                    <span className={`px-2 py-1 rounded text-sm ${
                      result.success
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {result.success ? 'Working' : 'Failed'}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    Model ID: {modelId}
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                    <pre className="text-xs overflow-auto text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {testResult && (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Test Results
            </h2>
            
            {testResult.multipleTests ? (
              <div className="space-y-4">
                {testResult.multipleTests.map((test: any, index: number) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-600 rounded p-4">
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                      {test.item} → {test.binType}
                    </h3>
                    <p className="text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-3 rounded">
                      {test.response.message}
                    </p>
                  </div>
                ))}
              </div>
            ) : testResult.apiTest ? (
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                  OpenRouter API Test Result
                </h3>
                <pre className="text-sm overflow-auto text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                  {JSON.stringify(testResult.apiTest, null, 2)}
                </pre>
              </div>
            ) : testResult.envInfo ? (
              <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                  Environment Information
                </h3>
                <pre className="text-sm overflow-auto text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                  {JSON.stringify(testResult.envInfo, null, 2)}
                </pre>
              </div>
            ) : testResult.quickTest ? (
              <div className="bg-yellow-50 dark:bg-yellow-900/30 p-4 rounded">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                  Quick Test Result: {testResult.quickTest.model}
                </h3>
                <pre className="text-sm overflow-auto text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                  {JSON.stringify(testResult.quickTest.result, null, 2)}
                </pre>
              </div>
            ) : testResult.apiKeyTest ? (
              <div className="bg-purple-50 dark:bg-purple-900/30 p-4 rounded">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                  API Key & Models Test Result
                </h3>
                <pre className="text-sm overflow-auto text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                  {JSON.stringify(testResult.apiKeyTest, null, 2)}
                </pre>
              </div>
            ) : testResult.basicModelTest ? (
              <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                  Basic Model Test Result: {testResult.basicModelTest.model}
                </h3>
                <pre className="text-sm overflow-auto text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                  {JSON.stringify(testResult.basicModelTest.result, null, 2)}
                </pre>
              </div>
            ) : (
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded">
                <pre className="text-sm overflow-auto text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                  {testResult.error ? 
                    `Error: ${testResult.error}` : 
                    testResult.message || JSON.stringify(testResult, null, 2)
                  }
                </pre>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default RAGTest;
