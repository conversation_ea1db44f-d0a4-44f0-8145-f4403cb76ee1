// Simple test script to check OpenRouter API
// Using built-in fetch (Node.js 18+)

const API_KEY = '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';
const SITE_URL = 'https://smart-trash-ai.lovable.app';
const SITE_NAME = 'Smart Trash AI';

async function testOpenRouterAPI() {
  console.log('Testing OpenRouter API...');
  console.log('API Key length:', API_KEY.length);
  console.log('API Key prefix:', API_KEY.substring(0, 20) + '...');

  const models = [
    'deepseek/deepseek-r1-0528-qwen3-8b:free',
    'mistralai/mistral-7b-instruct:free',
    'meta-llama/llama-3.3-8b-instruct:free'
  ];

  for (const model of models) {
    console.log(`\n--- Testing model: ${model} ---`);
    
    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'HTTP-Referer': SITE_URL,
          'X-Title': SITE_NAME,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: model,
          messages: [
            { role: 'user', content: 'Hello, can you respond with a simple greeting?' }
          ],
          max_tokens: 50,
          temperature: 0.7
        })
      });

      console.log('Response status:', response.status);
      console.log('Response statusText:', response.statusText);

      const responseText = await response.text();
      console.log('Raw response:', responseText);

      if (response.ok) {
        try {
          const data = JSON.parse(responseText);
          console.log('SUCCESS - Parsed response:', JSON.stringify(data, null, 2));
        } catch (parseError) {
          console.log('ERROR - Failed to parse JSON:', parseError.message);
        }
      } else {
        console.log('ERROR - API returned error status');
      }

    } catch (error) {
      console.log('ERROR - Exception:', error.message);
    }
  }
}

// Also test the models endpoint
async function testModelsEndpoint() {
  console.log('\n=== Testing Models Endpoint ===');
  
  try {
    const response = await fetch('https://openrouter.ai/api/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'HTTP-Referer': SITE_URL,
        'X-Title': SITE_NAME,
      }
    });

    console.log('Models endpoint status:', response.status);
    const responseText = await response.text();
    
    if (response.ok) {
      try {
        const data = JSON.parse(responseText);
        console.log('Total models available:', data.data ? data.data.length : 'Unknown');
        
        // Check if our target models are available
        const targetModels = [
          'deepseek/deepseek-r1-0528-qwen3-8b:free',
          'mistralai/mistral-7b-instruct:free',
          'meta-llama/llama-3.3-8b-instruct:free'
        ];
        
        targetModels.forEach(modelId => {
          const found = data.data.find(m => m.id === modelId);
          console.log(`${modelId}: ${found ? 'AVAILABLE' : 'NOT FOUND'}`);
        });
        
      } catch (parseError) {
        console.log('ERROR - Failed to parse models response:', parseError.message);
      }
    } else {
      console.log('ERROR - Models endpoint failed:', responseText);
    }

  } catch (error) {
    console.log('ERROR - Models endpoint exception:', error.message);
  }
}

// Run the tests
async function runTests() {
  await testModelsEndpoint();
  await testOpenRouterAPI();
}

runTests().catch(console.error);
